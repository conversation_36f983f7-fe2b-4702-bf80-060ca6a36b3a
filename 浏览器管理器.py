#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 - 浏览器管理器
版本: v2.2.1
作者: 浏览器多账号绿色版团队
描述: 核心浏览器实例管理功能，支持创建、启动、删除浏览器实例
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from typing import List, Dict, Optional
import json
from 配置管理器 import 配置管理器
from 快捷方式管理器 import 快捷方式管理器

class 浏览器管理器:
    """浏览器实例管理器"""

    def __init__(self):
        """初始化浏览器管理器"""
        self.配置器 = 配置管理器()
        self.快捷方式管理器实例 = 快捷方式管理器()
        self.项目根目录 = Path(__file__).parent.absolute()
        self.浏览器实例目录 = self.配置器.获取浏览器实例目录()
        self.chrome程序路径 = self.配置器.获取Chrome路径()
        self.默认图标目录 = self.配置器.获取默认图标目录()

        print("🔧 浏览器管理器初始化完成")

    def 获取浏览器列表(self) -> List[Dict]:
        """获取所有浏览器实例列表"""
        浏览器列表 = []

        if not self.浏览器实例目录.exists():
            return 浏览器列表

        for 实例目录 in self.浏览器实例目录.iterdir():
            if 实例目录.is_dir():
                浏览器信息 = self._获取浏览器信息(实例目录)
                if 浏览器信息:
                    浏览器列表.append(浏览器信息)

        return 浏览器列表

    def _获取浏览器信息(self, 实例目录: Path) -> Optional[Dict]:
        """获取单个浏览器实例信息"""
        try:
            浏览器名称 = 实例目录.name

            # 检查必要文件
            chrome_bin = 实例目录 / "Chrome-bin"
            data_dir = None
            for item in 实例目录.iterdir():
                if item.is_dir() and item.name.startswith("Data_"):
                    data_dir = item
                    break

            # 查找图标文件
            图标文件 = None
            for 扩展名 in ['.ico', '.png', '.jpg']:
                for 图标 in 实例目录.glob(f"*{扩展名}"):
                    图标文件 = 图标
                    break
                if 图标文件:
                    break

            # 查找启动脚本
            启动脚本文件 = 实例目录 / f"{浏览器名称}.bat"

            return {
                '名称': 浏览器名称,
                '路径': str(实例目录),
                'Chrome程序': chrome_bin.exists(),
                '数据目录': str(data_dir) if data_dir else None,
                '图标文件': str(图标文件) if 图标文件 else None,
                '快捷方式': 启动脚本文件.exists(),
                '状态': '正常' if chrome_bin.exists() and data_dir else '不完整'
            }

        except Exception as e:
            print(f"❌ 获取浏览器信息失败: {e}")
            return None

    def 创建浏览器实例(self, 浏览器名称: str, 图标类型: str = "generic") -> bool:
        """创建新的浏览器实例"""
        try:
            print(f"🆕 创建浏览器实例: {浏览器名称}")

            # 验证名称
            if not self._验证浏览器名称(浏览器名称):
                return False

            # 创建实例目录
            实例目录 = self.浏览器实例目录 / 浏览器名称
            if 实例目录.exists():
                print(f"❌ 浏览器实例已存在: {浏览器名称}")
                return False

            实例目录.mkdir(parents=True)
            print(f"✅ 创建实例目录: {实例目录}")

            # 复制Chrome程序文件
            if not self._复制Chrome程序(实例目录):
                shutil.rmtree(实例目录)
                return False

            # 创建独立数据目录
            数据目录 = 实例目录 / f"Data_{浏览器名称}"
            数据目录.mkdir()
            print(f"✅ 创建数据目录: {数据目录}")

            # 复制图标文件
            self._复制图标文件(实例目录, 图标类型)

            # 创建快捷方式
            self._创建快捷方式(实例目录, 浏览器名称, 图标类型)

            print(f"🎉 浏览器实例创建成功: {浏览器名称}")
            return True

        except Exception as e:
            print(f"❌ 创建浏览器实例失败: {e}")
            # 清理失败的创建
            实例目录 = self.浏览器实例目录 / 浏览器名称
            if 实例目录.exists():
                shutil.rmtree(实例目录)
            return False

    def _验证浏览器名称(self, 名称: str) -> bool:
        """验证浏览器名称是否合法"""
        if not 名称 or len(名称.strip()) == 0:
            print("❌ 浏览器名称不能为空")
            return False

        # 检查非法字符
        非法字符 = ['<', '>', ':', '"', '|', '?', '*', '/', '\\']
        for 字符 in 非法字符:
            if 字符 in 名称:
                print(f"❌ 浏览器名称包含非法字符: {字符}")
                return False

        return True

    def _复制Chrome程序(self, 实例目录: Path) -> bool:
        """复制Chrome程序文件到实例目录"""
        try:
            if not self.chrome程序路径 or not self.chrome程序路径.exists():
                print("❌ Chrome程序路径不存在")
                return False

            源Chrome目录 = self.chrome程序路径.parent / "App" / "Chrome-bin"
            目标Chrome目录 = 实例目录 / "Chrome-bin"

            if not 源Chrome目录.exists():
                print(f"❌ Chrome程序目录不存在: {源Chrome目录}")
                return False

            shutil.copytree(源Chrome目录, 目标Chrome目录)
            print(f"✅ 复制Chrome程序: {目标Chrome目录}")
            return True

        except Exception as e:
            print(f"❌ 复制Chrome程序失败: {e}")
            return False

    def _复制图标文件(self, 实例目录: Path, 图标类型: str):
        """复制图标文件到实例目录"""
        try:
            图标文件名 = f"{图标类型}.png"
            源图标路径 = self.默认图标目录 / 图标文件名

            if 源图标路径.exists():
                目标图标路径 = 实例目录 / f"{图标类型}.png"
                shutil.copy2(源图标路径, 目标图标路径)
                print(f"✅ 复制图标文件: {目标图标路径}")
            else:
                print(f"⚠️ 图标文件不存在: {源图标路径}")

        except Exception as e:
            print(f"❌ 复制图标文件失败: {e}")

    def _创建快捷方式(self, 实例目录: Path, 浏览器名称: str, 图标类型: str = "chrome"):
        """创建快捷方式"""
        try:
            # 首先创建本地启动脚本（必需）
            self._创建本地启动脚本(实例目录, 浏览器名称)

            # 然后尝试创建桌面快捷方式（可选）
            try:
                成功 = self.快捷方式管理器实例.创建浏览器快捷方式(
                    浏览器名称=浏览器名称,
                    实例目录=实例目录,
                    图标类型=图标类型
                )

                if 成功:
                    print(f"✅ 桌面快捷方式创建成功: {浏览器名称}")
                else:
                    print(f"⚠️ 桌面快捷方式创建失败: {浏览器名称}")

            except Exception as e:
                print(f"⚠️ 桌面快捷方式创建异常: {e}")

        except Exception as e:
            print(f"❌ 创建快捷方式失败: {e}")
            # 确保至少有本地启动脚本
            self._创建本地启动脚本(实例目录, 浏览器名称)

    def _创建本地启动脚本(self, 实例目录: Path, 浏览器名称: str):
        """创建本地启动脚本（必需）- 使用相对路径确保跨电脑兼容"""
        try:
            启动脚本路径 = 实例目录 / f"{浏览器名称}.bat"

            # 使用相对路径，确保跨电脑兼容性
            相对chrome_exe = ".\\Chrome-bin\\chrome.exe"
            相对数据目录 = f".\\Data_{浏览器名称}"

            启动命令 = f'"{相对chrome_exe}" --user-data-dir="{相对数据目录}" --no-first-run --no-default-browser-check'

            with open(启动脚本路径, 'w', encoding='utf-8') as f:
                f.write('@echo off\n')
                f.write(f'cd /d "%~dp0"\n')  # 切换到脚本所在目录
                f.write(f'{启动命令}\n')

            print(f"✅ 创建本地启动脚本（相对路径）: {启动脚本路径}")

        except Exception as e:
            print(f"❌ 创建本地启动脚本失败: {e}")

    def 启动浏览器(self, 浏览器名称: str) -> bool:
        """启动指定的浏览器实例"""
        try:
            print(f"🚀 启动浏览器: {浏览器名称}")

            实例目录 = self.浏览器实例目录 / 浏览器名称
            if not 实例目录.exists():
                print(f"❌ 浏览器实例不存在: {浏览器名称}")
                return False

            chrome_exe = 实例目录 / "Chrome-bin" / "chrome.exe"
            数据目录 = 实例目录 / f"Data_{浏览器名称}"

            if not chrome_exe.exists():
                print(f"❌ Chrome程序不存在: {chrome_exe}")
                return False

            # 启动Chrome
            启动命令 = [
                str(chrome_exe),
                f"--user-data-dir={数据目录}",
                "--no-first-run",
                "--no-default-browser-check"
            ]

            subprocess.Popen(启动命令, cwd=str(实例目录))
            print(f"✅ 浏览器启动成功: {浏览器名称}")
            return True

        except Exception as e:
            print(f"❌ 启动浏览器失败: {e}")
            return False

    def 删除浏览器实例(self, 浏览器名称: str, 需要确认: bool = True) -> bool:
        """删除指定的浏览器实例

        Args:
            浏览器名称: 要删除的浏览器实例名称
            需要确认: 是否需要命令行确认（GUI调用时应设为False）
        """
        try:
            print(f"🗑️ 删除浏览器实例: {浏览器名称}")

            实例目录 = self.浏览器实例目录 / 浏览器名称
            if not 实例目录.exists():
                print(f"❌ 浏览器实例不存在: {浏览器名称}")
                return False

            # 只在命令行模式下需要确认
            if 需要确认:
                确认 = input(f"⚠️ 确定要删除浏览器实例 '{浏览器名称}' 吗？(y/N): ")
                if 确认.lower() != 'y':
                    print("❌ 用户取消删除操作")
                    return False

            # 删除目录
            shutil.rmtree(实例目录)
            print(f"✅ 浏览器实例删除成功: {浏览器名称}")
            return True

        except Exception as e:
            print(f"❌ 删除浏览器实例失败: {e}")
            return False

    def 发送到桌面(self, 浏览器名称: str) -> bool:
        """将浏览器快捷方式发送到桌面"""
        try:
            print(f"🖥️ 发送浏览器到桌面: {浏览器名称}")

            实例目录 = self.浏览器实例目录 / 浏览器名称
            if not 实例目录.exists():
                print(f"❌ 浏览器实例不存在: {浏览器名称}")
                return False

            # 查找图标类型
            图标类型 = "chrome"  # 默认
            for 扩展名 in ['.ico', '.png', '.jpg']:
                for 图标文件 in 实例目录.glob(f"*{扩展名}"):
                    图标类型 = 图标文件.stem
                    break
                if 图标类型 != "chrome":
                    break

            # 创建桌面快捷方式
            成功 = self.快捷方式管理器实例.创建浏览器快捷方式(
                浏览器名称=浏览器名称,
                实例目录=实例目录,
                图标类型=图标类型
            )

            if 成功:
                print(f"✅ 浏览器已发送到桌面: {浏览器名称}")
            else:
                print(f"❌ 发送到桌面失败: {浏览器名称}")

            return 成功

        except Exception as e:
            print(f"❌ 发送到桌面异常: {e}")
            return False

def 显示菜单():
    """显示主菜单"""
    print("\n" + "="*50)
    print("🌟 浏览器多账号绿色版 - 命令行管理器")
    print("="*50)
    print("1. 📋 查看浏览器列表")
    print("2. 🆕 创建浏览器实例")
    print("3. 🚀 启动浏览器")
    print("4. 🖥️ 发送到桌面")
    print("5. 🗑️ 删除浏览器实例")
    print("6. ❓ 帮助信息")
    print("0. 👋 退出程序")
    print("="*50)

def 显示浏览器列表(管理器: 浏览器管理器):
    """显示浏览器列表"""
    浏览器列表 = 管理器.获取浏览器列表()

    if not 浏览器列表:
        print("\n📋 当前没有浏览器实例")
        return

    print(f"\n📋 浏览器实例列表 (共 {len(浏览器列表)} 个):")
    print("-" * 80)
    print(f"{'序号':<4} {'名称':<20} {'状态':<8} {'数据目录':<10} {'图标':<6} {'快捷方式':<8}")
    print("-" * 80)

    for i, 浏览器 in enumerate(浏览器列表, 1):
        数据状态 = "✅" if 浏览器['数据目录'] else "❌"
        图标状态 = "✅" if 浏览器['图标文件'] else "❌"
        快捷方式状态 = "✅" if 浏览器['快捷方式'] else "❌"

        print(f"{i:<4} {浏览器['名称']:<20} {浏览器['状态']:<8} {数据状态:<10} {图标状态:<6} {快捷方式状态:<8}")

def main():
    """主函数"""
    try:
        管理器 = 浏览器管理器()

        while True:
            显示菜单()
            选择 = input("\n请选择操作 (0-6): ").strip()

            if 选择 == "0":
                print("\n👋 感谢使用，再见！")
                break
            elif 选择 == "1":
                显示浏览器列表(管理器)
            elif 选择 == "2":
                浏览器名称 = input("\n请输入浏览器名称: ").strip()
                if 浏览器名称:
                    图标类型 = input("请输入图标类型 (chrome/firefox/edge/generic) [generic]: ").strip() or "generic"
                    管理器.创建浏览器实例(浏览器名称, 图标类型)
            elif 选择 == "3":
                浏览器名称 = input("\n请输入要启动的浏览器名称: ").strip()
                if 浏览器名称:
                    管理器.启动浏览器(浏览器名称)
            elif 选择 == "4":
                浏览器名称 = input("\n请输入要发送到桌面的浏览器名称: ").strip()
                if 浏览器名称:
                    管理器.发送到桌面(浏览器名称)
            elif 选择 == "5":
                浏览器名称 = input("\n请输入要删除的浏览器名称: ").strip()
                if 浏览器名称:
                    管理器.删除浏览器实例(浏览器名称)
            elif 选择 == "6":
                print("""
📖 帮助信息:
  • 创建浏览器实例：为每个账号创建独立的浏览器环境
  • 启动浏览器：启动指定的浏览器实例
  • 删除浏览器实例：完全删除浏览器实例及其数据
  • 每个浏览器实例都有独立的用户数据，互不影响
  • 支持自定义图标，让不同浏览器更容易区分
                """)
            else:
                print("❌ 无效选择，请重新输入")

            if 选择 != "0":
                input("\n按回车键继续...")

    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序运行时发生错误: {e}")

if __name__ == "__main__":
    main()
